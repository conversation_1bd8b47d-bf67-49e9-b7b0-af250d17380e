/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Fira Code', 'Courier New', monospace;
    background: #0a0a0a;
    color: #00ff00;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Terminal Container */
.terminal-container {
    max-width: 1200px;
    margin: 20px auto;
    background: #1a1a1a;
    border-radius: 8px;
    box-shadow: 0 0 30px rgba(0, 255, 0, 0.3);
    overflow: hidden;
    min-height: calc(100vh - 40px);
}

/* Terminal Header */
.terminal-header {
    background: #2d2d2d;
    padding: 12px 20px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #444;
}

.terminal-controls {
    display: flex;
    gap: 8px;
    margin-right: 20px;
}

.control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    cursor: pointer;
}

.control.close {
    background: #ff5f56;
}

.control.minimize {
    background: #ffbd2e;
}

.control.maximize {
    background: #27ca3f;
}

.terminal-title {
    color: #ccc;
    font-size: 14px;
    font-weight: 500;
}

/* Terminal Content */
.terminal-content {
    padding: 20px;
    background: #0a0a0a;
    min-height: calc(100vh - 100px);
}

.terminal-section {
    margin-bottom: 30px;
}

/* Prompt and Command Styling */
.prompt-line {
    margin-bottom: 10px;
}

.prompt {
    color: #00ff00;
    font-weight: 600;
}

.command {
    color: #ffffff;
    margin-left: 10px;
}

.output {
    margin-left: 20px;
    color: #cccccc;
}

/* ASCII Art */
.ascii-art {
    color: #00ff00;
    font-size: 10px;
    line-height: 1;
    margin: 20px 0;
    overflow-x: auto;
}

.welcome-text {
    color: #00ff00;
    font-size: 18px;
    font-weight: 600;
    margin: 20px 0 10px 0;
}

.info-text {
    color: #888;
    margin-bottom: 20px;
}

/* File List Styling */
.file-list {
    font-family: 'Fira Code', monospace;
}

.file-item {
    padding: 5px 0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-item:hover {
    background: rgba(0, 255, 0, 0.1);
    padding-left: 10px;
}

.directory {
    color: #00bfff;
    font-weight: 600;
}

.file {
    color: #ffffff;
}

/* Project Grid */
.project-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.project-card {
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s ease;
}

.project-card:hover {
    border-color: #00ff00;
    box-shadow: 0 0 15px rgba(0, 255, 0, 0.2);
}

.project-card h3 {
    color: #00ff00;
    margin-bottom: 10px;
}

.project-card p {
    color: #ccc;
    margin-bottom: 15px;
}

.tech-stack {
    color: #888;
    font-size: 12px;
    font-style: italic;
}

/* Form Styling */
.terminal-form {
    max-width: 600px;
    margin-top: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    color: #00ff00;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 4px;
    padding: 12px;
    color: #ffffff;
    font-family: 'Fira Code', monospace;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #00ff00;
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Checkbox Styling */
.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #ccc;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin-right: 10px;
    transform: scale(1.2);
}

/* Submit Button */
.submit-btn {
    background: linear-gradient(45deg, #00ff00, #00cc00);
    color: #000;
    border: none;
    padding: 12px 30px;
    border-radius: 4px;
    font-family: 'Fira Code', monospace;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
}

.submit-btn:hover {
    background: linear-gradient(45deg, #00cc00, #009900);
    box-shadow: 0 0 15px rgba(0, 255, 0, 0.5);
    transform: translateY(-2px);
}

/* Error Messages */
.error-message {
    color: #ff4444;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

/* Form Output */
.form-output {
    margin-top: 20px;
    padding: 15px;
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 4px;
    display: none;
}

.form-output.success {
    border-color: #00ff00;
    background: rgba(0, 255, 0, 0.1);
}

.form-output.error {
    border-color: #ff4444;
    background: rgba(255, 68, 68, 0.1);
}

/* Command Input */
.command-input {
    display: flex;
    align-items: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #333;
}

.command-input input {
    flex: 1;
    background: transparent;
    border: none;
    color: #ffffff;
    font-family: 'Fira Code', monospace;
    font-size: 14px;
    margin-left: 10px;
    outline: none;
}

.command-input input::placeholder {
    color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
    .terminal-container {
        margin: 10px;
        border-radius: 0;
        min-height: calc(100vh - 20px);
    }
    
    .terminal-content {
        padding: 15px;
    }
    
    .ascii-art {
        font-size: 8px;
        overflow-x: scroll;
    }
    
    .project-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .terminal-form {
        max-width: 100%;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 10px;
        font-size: 16px; /* Prevents zoom on iOS */
    }
    
    .submit-btn {
        width: 100%;
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .terminal-header {
        padding: 10px 15px;
    }
    
    .terminal-title {
        font-size: 12px;
    }
    
    .ascii-art {
        font-size: 6px;
    }
    
    .welcome-text {
        font-size: 16px;
    }
    
    .project-card {
        padding: 15px;
    }
}

/* Animation for typing effect */
@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.typing-effect {
    overflow: hidden;
    white-space: nowrap;
    animation: typing 2s steps(40, end), blink 1s infinite;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
    background: #333;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}
