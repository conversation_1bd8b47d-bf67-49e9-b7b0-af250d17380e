// Terminal Website JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the terminal interface
    initializeTerminal();
    initializeNavigation();
    initializeForms();
    initializeCommandInput();
});

// Terminal initialization
function initializeTerminal() {
    // Add typing effect to welcome text
    const welcomeText = document.querySelector('.welcome-text');
    if (welcomeText) {
        welcomeText.classList.add('typing-effect');
    }
    
    // Simulate terminal startup
    setTimeout(() => {
        console.log('Terminal initialized successfully');
    }, 1000);
}

// Navigation system
function initializeNavigation() {
    const fileItems = document.querySelectorAll('.file-item');
    
    fileItems.forEach(item => {
        item.addEventListener('click', function() {
            const target = this.getAttribute('data-target');
            if (target) {
                showSection(target);
            }
        });
    });
}

function showSection(sectionId) {
    // Hide all sections
    const sections = document.querySelectorAll('.terminal-section');
    sections.forEach(section => {
        if (section.id && section.id !== sectionId) {
            section.style.display = 'none';
        }
    });
    
    // Show target section
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.style.display = 'block';
        targetSection.scrollIntoView({ behavior: 'smooth' });
    }
}

// Form initialization and validation
function initializeForms() {
    // Contact form
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', handleContactForm);
        
        // Real-time validation
        const contactInputs = contactForm.querySelectorAll('input, select, textarea');
        contactInputs.forEach(input => {
            input.addEventListener('blur', () => validateContactField(input));
            input.addEventListener('input', () => clearError(input));
        });
    }
    
    // Registration form
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegisterForm);
        
        // Real-time validation
        const registerInputs = registerForm.querySelectorAll('input');
        registerInputs.forEach(input => {
            input.addEventListener('blur', () => validateRegisterField(input));
            input.addEventListener('input', () => clearError(input));
        });
    }
}

// Contact form handler
function handleContactForm(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const data = Object.fromEntries(formData);
    
    // Validate all fields
    let isValid = true;
    const fields = ['name', 'email', 'subject', 'message'];
    
    fields.forEach(field => {
        const input = document.getElementById(field);
        if (!validateContactField(input)) {
            isValid = false;
        }
    });
    
    if (isValid) {
        displayFormOutput('contact', 'success', 'Message sent successfully!', data);
        e.target.reset();
    } else {
        displayFormOutput('contact', 'error', 'Please fix the errors above.');
    }
}

// Registration form handler
function handleRegisterForm(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const data = Object.fromEntries(formData);
    
    // Validate all fields
    let isValid = true;
    const fields = ['username', 'regEmail', 'password', 'confirmPassword', 'age', 'terms'];
    
    fields.forEach(field => {
        const input = document.getElementById(field);
        if (!validateRegisterField(input)) {
            isValid = false;
        }
    });
    
    if (isValid) {
        displayFormOutput('register', 'success', 'Registration successful!', data);
        e.target.reset();
    } else {
        displayFormOutput('register', 'error', 'Please fix the errors above.');
    }
}

// Contact form field validation
function validateContactField(input) {
    const value = input.value.trim();
    const fieldName = input.name;
    let isValid = true;
    let errorMessage = '';
    
    switch (fieldName) {
        case 'name':
            if (!value) {
                errorMessage = 'Name is required';
                isValid = false;
            } else if (value.length < 2) {
                errorMessage = 'Name must be at least 2 characters';
                isValid = false;
            } else if (!/^[a-zA-Z\s]+$/.test(value)) {
                errorMessage = 'Name can only contain letters and spaces';
                isValid = false;
            }
            break;
            
        case 'email':
            if (!value) {
                errorMessage = 'Email is required';
                isValid = false;
            } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                errorMessage = 'Please enter a valid email address';
                isValid = false;
            }
            break;
            
        case 'phone':
            if (value && !/^[\+]?[1-9][\d]{0,15}$/.test(value.replace(/[\s\-\(\)]/g, ''))) {
                errorMessage = 'Please enter a valid phone number';
                isValid = false;
            }
            break;
            
        case 'subject':
            if (!value) {
                errorMessage = 'Please select a subject';
                isValid = false;
            }
            break;
            
        case 'message':
            if (!value) {
                errorMessage = 'Message is required';
                isValid = false;
            } else if (value.length < 10) {
                errorMessage = 'Message must be at least 10 characters';
                isValid = false;
            }
            break;
    }
    
    displayFieldError(fieldName, errorMessage);
    return isValid;
}

// Registration form field validation
function validateRegisterField(input) {
    const value = input.value.trim();
    const fieldName = input.name || input.id;
    let isValid = true;
    let errorMessage = '';
    
    switch (fieldName) {
        case 'username':
            if (!value) {
                errorMessage = 'Username is required';
                isValid = false;
            } else if (value.length < 3) {
                errorMessage = 'Username must be at least 3 characters';
                isValid = false;
            } else if (!/^[a-zA-Z0-9_]+$/.test(value)) {
                errorMessage = 'Username can only contain letters, numbers, and underscores';
                isValid = false;
            }
            break;
            
        case 'email':
        case 'regEmail':
            if (!value) {
                errorMessage = 'Email is required';
                isValid = false;
            } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                errorMessage = 'Please enter a valid email address';
                isValid = false;
            }
            break;
            
        case 'password':
            if (!value) {
                errorMessage = 'Password is required';
                isValid = false;
            } else if (value.length < 8) {
                errorMessage = 'Password must be at least 8 characters';
                isValid = false;
            } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
                errorMessage = 'Password must contain at least one uppercase letter, one lowercase letter, and one number';
                isValid = false;
            }
            break;
            
        case 'confirmPassword':
            const password = document.getElementById('password').value;
            if (!value) {
                errorMessage = 'Please confirm your password';
                isValid = false;
            } else if (value !== password) {
                errorMessage = 'Passwords do not match';
                isValid = false;
            }
            break;
            
        case 'age':
            const age = parseInt(value);
            if (!value) {
                errorMessage = 'Age is required';
                isValid = false;
            } else if (age < 13 || age > 120) {
                errorMessage = 'Age must be between 13 and 120';
                isValid = false;
            }
            break;
            
        case 'terms':
            if (!input.checked) {
                errorMessage = 'You must agree to the terms and conditions';
                isValid = false;
            }
            break;
    }
    
    const errorId = fieldName === 'regEmail' ? 'regEmailError' : fieldName + 'Error';
    displayFieldError(errorId.replace('Error', ''), errorMessage);
    return isValid;
}

// Display field error
function displayFieldError(fieldName, message) {
    const errorId = fieldName === 'regEmail' ? 'regEmailError' : fieldName + 'Error';
    const errorElement = document.getElementById(errorId);
    if (errorElement) {
        errorElement.textContent = message;
    }
}

// Clear field error
function clearError(input) {
    const fieldName = input.name || input.id;
    const errorId = fieldName === 'regEmail' ? 'regEmailError' : fieldName + 'Error';
    const errorElement = document.getElementById(errorId);
    if (errorElement) {
        errorElement.textContent = '';
    }
}

// Display form output
function displayFormOutput(formType, status, message, data = null) {
    const outputId = formType + 'Output';
    const outputElement = document.getElementById(outputId);
    
    if (outputElement) {
        outputElement.className = `form-output ${status}`;
        
        let content = `<div class="prompt-line">
            <span class="prompt">user@terminal:~$</span>
            <span class="command">${status === 'success' ? 'echo "Success"' : 'echo "Error"'}</span>
        </div>
        <div class="output">
            <p>${message}</p>`;
        
        if (data && status === 'success') {
            content += '<h4>Submitted Data:</h4><pre>';
            for (const [key, value] of Object.entries(data)) {
                if (key !== 'password' && key !== 'confirmPassword') {
                    content += `${key}: ${value}\n`;
                }
            }
            content += '</pre>';
        }
        
        content += '</div>';
        outputElement.innerHTML = content;
        outputElement.style.display = 'block';
        
        // Auto-hide after 5 seconds for success messages
        if (status === 'success') {
            setTimeout(() => {
                outputElement.style.display = 'none';
            }, 5000);
        }
    }
}

// Command input system
function initializeCommandInput() {
    const commandInput = document.getElementById('commandInput');
    
    if (commandInput) {
        commandInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const command = this.value.trim().toLowerCase();
                executeCommand(command);
                this.value = '';
            }
        });
    }
}

// Command execution
function executeCommand(command) {
    const commands = {
        'help': 'Available commands: help, clear, about, contact, projects, register, whoami, date, ls',
        'clear': () => {
            // Hide all sections
            const sections = document.querySelectorAll('.terminal-section[id]');
            sections.forEach(section => section.style.display = 'none');
        },
        'about': () => showSection('about'),
        'contact': () => showSection('contact'),
        'projects': () => showSection('projects'),
        'register': () => showSection('register'),
        'whoami': 'user@terminal',
        'date': new Date().toString(),
        'ls': 'about/ contact/ projects/ register.sh'
    };
    
    if (typeof commands[command] === 'function') {
        commands[command]();
    } else if (commands[command]) {
        console.log(commands[command]);
        // You could display this in a terminal output area if desired
    } else {
        console.log(`Command not found: ${command}. Type 'help' for available commands.`);
    }
}

// Utility functions
function formatData(data) {
    return Object.entries(data)
        .map(([key, value]) => `${key}: ${value}`)
        .join('\n');
}

// Add some terminal-like interactions
document.addEventListener('click', function(e) {
    // Focus command input when clicking on terminal
    if (e.target.closest('.terminal-container')) {
        const commandInput = document.getElementById('commandInput');
        if (commandInput && !e.target.closest('form')) {
            setTimeout(() => commandInput.focus(), 100);
        }
    }
});

// Console welcome message
console.log(`
████████╗███████╗██████╗ ███╗   ███╗██╗███╗   ██╗ █████╗ ██╗     
╚══██╔══╝██╔════╝██╔══██╗████╗ ████║██║████╗  ██║██╔══██╗██║     
   ██║   █████╗  ██████╔╝██╔████╔██║██║██╔██╗ ██║███████║██║     
   ██║   ██╔══╝  ██╔══██╗██║╚██╔╝██║██║██║╚██╗██║██╔══██║██║     
   ██║   ███████╗██║  ██║██║ ╚═╝ ██║██║██║ ╚████║██║  ██║███████╗
   ╚═╝   ╚══════╝╚═╝  ╚═╝╚═╝     ╚═╝╚═╝╚═╝  ╚═══╝╚═╝  ╚═╝╚══════╝

Welcome to Terminal Interface!
Type commands in the input field or click on the file system above.
`);
